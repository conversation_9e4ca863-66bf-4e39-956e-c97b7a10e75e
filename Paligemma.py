#!/usr/bin/env python3
# visualize_paligemma2_prompt_attn_v5_localpath_sortedlayers_with_info_newdir.py
# Visualizes Prompt Token attention to Image Patches for PaliGemma-2 models.
# Uses local image path instead of URL.
# Based on visualize_prompt_attention_grids_v5.py logic.
# Uses BF16 if available.
# Original Views: Raw scores. Average Views: Global + Local Norm Grids.
# MODIFIED: Ensures layer average grids are sorted numerically by layer index.
# MODIFIED: Adds Prompt and Model Name to the top margin of plots.
# MODIFIED: Changed output directory naming to {model_base_name}_{safe_prompt_name}.

import argparse
import torch
from PIL import Image
import matplotlib.pyplot as plt
import numpy as np
from transformers import AutoProcessor, PaliGemmaForConditionalGeneration
import torch.nn.functional as F
import math
import os
# import traceback
import re
import torchvision.transforms as T
from collections import defaultdict
import textwrap # For wrapping long text

# --- 可视化相关设置 ---
AVERAGE_HEADS = True
HEATMAP_ALPHA = 0.6
DEFAULT_INTERPOLATION = 'nearest'
GRID_MAX_COLS = 5
INFO_TEXT_WRAP_WIDTH = 100 # Adjust as needed
# -----------------------

# --- 全局变量 ---
g_num_image_patches = 0
g_prompt_start_idx = 0
g_prompt_end_idx = 0
# ----------------

# --- Helper Functions ---
def make_safe_filename(text, max_len=50):
    """将文本转换为安全的文件名部分。"""
    # Keep <image> placeholder for prompt part of directory name if desired
    # text = text.replace('<image>', 'img_') # Optional: Replace placeholder
    text = text.replace(' ', '_'); text = re.sub(r'[<>:"/\\|?*]', '', text)
    special_tokens = {'<bos>': 'BOS', '<eos>': 'EOS', '<pad>': 'PAD', '<unk>': 'UNK', '\n': 'NL'}
    for sp, rep in special_tokens.items(): text = text.replace(sp, rep)
    if text.startswith('_') and len(text) > 1: text = text[1:]
    return text[:max_len]

def make_safe_dirname(text, max_len=100):
    """将文本转换为安全的目录名部分 (more aggressive cleaning for dir name)。"""
    text = text.strip()
    # Replace <image> placeholder specifically for directory names
    text = text.replace('<image>', 'img')
    text = text.replace(' ', '_')
    # Remove a wider range of potentially problematic characters for directory names
    text = re.sub(r'[<>:"/\\|?*.,;!@#$%^&()=+`~\[\]{}]', '', text)
    # Collapse multiple underscores
    text = re.sub(r'_+', '_', text)
    # Remove leading/trailing underscores
    text = text.strip('_')
    return text[:max_len]

def get_model_basename(model_name_or_path):
    """Extracts the base name of the model from a path or ID."""
    return os.path.basename(model_name_or_path)

# --- 修改 inspect_inputs 处理本地路径 ---
def inspect_inputs(processor, image_path, prompt, device, dtype):
    """加载并检查 processor 处理后的 inputs，提取序列构成信息和 prompt token 列表。"""
    global g_num_image_patches, g_prompt_start_idx, g_prompt_end_idx
    prompt_token_list = []
    print("--- Input Inspection ---"); print(f"Image Path: {image_path}"); print(f"Prompt: \"{prompt}\""); print(f"Device: {device}, Dtype: {dtype}"); print("-------------------------")
    try:
        print(f"Loading image from {image_path}..."); image = Image.open(image_path).convert("RGB"); image_size = image.size; print(f"Image loaded successfully. Original size: {image_size}")
    except FileNotFoundError: print(f"Error: Image file not found at {image_path}"); return None, None, None
    except Exception as e: print(f"Error loading image: {e}"); return None, None, None
    try:
        print("Processing image and text prompt with processor..."); inputs = processor(text=prompt, images=image, return_tensors="pt").to(device)
        print("Inputs moved successfully."); print("-------------------------")
        print("Input Tensor Details:"); input_ids = None; attention_mask = None; full_token_list = []
        for key, value in inputs.items():
            print(f"  Key: '{key}'")
            if isinstance(value, torch.Tensor):
                print(f"    Shape: {value.shape}"); print(f"    Dtype: {value.dtype}"); print(f"    Device: {value.device}")
                if key == 'input_ids':
                    input_ids = value
                    try:
                        decoded_full = processor.batch_decode(value, skip_special_tokens=False)[0]; print(f"----------\nToken Information:")
                        print(f"  Number of token IDs: {value.shape[-1]}"); print(f"  Decoded Tokens (including special): '{decoded_full}'")
                        current_tokens = processor.tokenizer.convert_ids_to_tokens(value[0].tolist()); full_token_list = current_tokens; print(f"  Full Tokens List: {full_token_list}")
                    except Exception as decode_err: print(f"    Could not decode or get tokens list: {decode_err}")
                elif key == 'attention_mask': attention_mask = value;
                elif value.is_floating_point():
                     if key == 'pixel_values': print(f"    Values ({key}): Min={value.min():.4f}, Max={value.max():.4f}, Mean={value.mean():.4f}")
                     else: print(f"    Values ({key}): Min={value.min():.4f}, Max={value.max():.4f}, Mean={value.mean():.4f}")
                else:
                     if value.numel() < 50: print(f"    Values ({key}): {value.tolist()}")
            else: print(f"    Value: {value}")
            print("----------")
        if input_ids is not None and attention_mask is not None and full_token_list:
            seq_len = input_ids.shape[-1]
            print("Consistency Checks:"); print(f"  input_ids length ({seq_len}) vs attention_mask length ({attention_mask.shape[-1]}): {'Match 👍' if seq_len == attention_mask.shape[-1] else 'Mismatch 👎'}"); print("-------------------------")
            bos_token_id = processor.tokenizer.bos_token_id
            bos_token_str = processor.tokenizer.decode([bos_token_id])
            try:
                bos_index = full_token_list.index(bos_token_str)
                g_num_image_patches = bos_index; g_prompt_start_idx = bos_index + 1
                print(f"Deduced Number of Image Patch Placeholders: {g_num_image_patches} (based on BOS index)"); print(f"Found BOS token ('{bos_token_str}') at index: {bos_index}")
            except ValueError:
                print(f"Warning: BOS token ('{bos_token_str}') not found in token list. Assuming image patches = 0 and text starts at 0."); g_num_image_patches = 0; g_prompt_start_idx = 0; bos_index = -1
            g_prompt_end_idx = seq_len; last_token_id = input_ids[0, -1].item(); eos_token_id = processor.tokenizer.eos_token_id
            try: newline_id = processor.tokenizer.convert_tokens_to_ids('\n')
            except KeyError: newline_id = -1
            if last_token_id == eos_token_id or (newline_id != -1 and last_token_id == newline_id):
                g_prompt_end_idx = seq_len - 1; print(f"Excluded last token (EOS or Newline). Prompt End Index (exclusive): {g_prompt_end_idx}")
            else: print(f"Prompt End Index (exclusive): {g_prompt_end_idx}")
            if g_prompt_start_idx < g_prompt_end_idx:
                prompt_token_list = full_token_list[g_prompt_start_idx:g_prompt_end_idx]; print(f"Extracted Prompt Tokens: {prompt_token_list}")
            else: print("Warning: No prompt tokens identified based on indices.")
            print("\n--- Deduced Sequence Structure ---"); print(f"Image Patch Placeholders: Indices 0 to {g_num_image_patches - 1} (Count: {g_num_image_patches})")
            if bos_index != -1: print(f"BOS Token: Index {bos_index}")
            print(f"Prompt Tokens: Indices {g_prompt_start_idx} to {g_prompt_end_idx - 1} (Count: {g_prompt_end_idx - g_prompt_start_idx})")
            if g_prompt_end_idx < seq_len: print(f"Trailing Token(s): Indices {g_prompt_end_idx} to {seq_len - 1}")
            print("--------------------------------\n")
        else: print("Could not perform detailed sequence analysis.")
        print("Input inspection complete.")
        return inputs, image_size, prompt_token_list
    except Exception as e: print(f"Error processing inputs: {e}"); return None, None, None
# ------------------------------------------

def get_all_attention_maps(model, inputs_on_device, device, dtype):
    """执行模型前向传播并提取所有层的注意力图 (PaliGemma specific)。"""
    if inputs_on_device is None: return None
    seq_len = inputs_on_device['input_ids'].shape[-1]; print(f"\nPerforming forward pass with output_attentions=True (Sequence Length: {seq_len})...")
    model.eval(); attentions_all_layers = None
    try:
        with torch.no_grad():
            with torch.autocast(device_type=device.type, dtype=dtype, enabled=(dtype != torch.float32)): outputs = model(**inputs_on_device, output_attentions=True)
        if hasattr(outputs, 'language_model_outputs') and hasattr(outputs.language_model_outputs, 'attentions') and outputs.language_model_outputs.attentions is not None:
            attentions_all_layers = outputs.language_model_outputs.attentions; print("Extracted attentions from outputs.language_model_outputs.attentions")
        elif hasattr(outputs, 'attentions') and outputs.attentions is not None:
             attentions_all_layers = outputs.attentions; print("Extracted attentions from outputs.attentions")
        elif isinstance(outputs, dict) and 'attentions' in outputs and outputs['attentions'] is not None:
             attentions_all_layers = outputs['attentions']; print("Extracted attentions from outputs['attentions']")
        else:
            print("Error: Could not find 'attentions' in PaliGemma model output."); print("Available keys:", outputs.keys() if isinstance(outputs, dict) else "N/A");
            if hasattr(outputs, 'language_model_outputs'): print("language_model_outputs keys:", outputs.language_model_outputs.keys() if isinstance(outputs.language_model_outputs, dict) else "N/A")
            return None
        num_layers = len(attentions_all_layers); print(f"Received attentions for {num_layers} layers.")
        if num_layers > 0:
            first_layer_att = attentions_all_layers[0]
            if first_layer_att.shape[-1] != seq_len: print(f"Warning: Attention map KV seq len ({first_layer_att.shape[-1]}) MISMATCHES input_ids len ({seq_len}). Check model structure.")
            else: print("Attention map KV sequence length matches input_ids length. 👍")
        else: print("Warning: Received 0 attention layers."); return None
    except Exception as e: print(f"Error during forward pass: {e}"); return None
    return attentions_all_layers

def calculate_and_visualize_single_token_attention(
    att_map, inputs_on_device, output_path, layer_index, token_index_in_prompt, token_string,
    interpolation_mode=DEFAULT_INTERPOLATION, alpha=HEATMAP_ALPHA, average_heads=AVERAGE_HEADS
):
    """计算并可视化单个 prompt token 对图像 patch 的原始注意力图 (不归一化)。返回 numpy array (未归一化)。"""
    global g_num_image_patches, g_prompt_start_idx, g_prompt_end_idx
    current_token_global_idx = g_prompt_start_idx + token_index_in_prompt
    if not (0 <= current_token_global_idx < g_prompt_end_idx): return None
    if g_num_image_patches == 0: print(f"Warning (Layer {layer_index}, Token '{token_string}'): Num image patches is 0, cannot visualize attention to image."); return None
    if 'pixel_values' not in inputs_on_device: return None
    try: pv = inputs_on_device['pixel_values'][0]; img_height, img_width = pv.shape[-2], pv.shape[-1]; img_tensor_224 = pv.cpu().float().clamp(0, 1); img_224 = T.ToPILImage()(img_tensor_224)
    except Exception as e: print(f"Error processing pixel_values: {e}"); return None
    if att_map is None: return None
    if att_map.ndim != 4 or att_map.shape[0] == 0: print(f"Error or Warning (Layer {layer_index}, Token '{token_string}'): Unexpected input attention map shape: {att_map.shape}. Expected 4D tensor. Skipping."); return None
    att_map = att_map[0]; num_img_patches = g_num_image_patches; num_heads = att_map.shape[0]; query_seq_len = att_map.shape[1]; kv_seq_len = att_map.shape[2]
    if current_token_global_idx >= query_seq_len: print(f"Error (Layer {layer_index}, Token '{token_string}'): Query index {current_token_global_idx} >= query_seq_len {query_seq_len}. Skipping."); return None
    if num_img_patches > kv_seq_len: print(f"Error (Layer {layer_index}, Token '{token_string}'): Num image patches {num_img_patches} > kv_seq_len {kv_seq_len}. Skipping."); return None
    if average_heads: att_map_processed = att_map.mean(dim=0)
    else: att_map_processed = att_map[0]
    image_att_scores = att_map_processed[current_token_global_idx, :num_img_patches]
    if image_att_scores.numel() == 0 or image_att_scores.shape[0] != num_img_patches: return None
    num_patches_sqrt_float = math.sqrt(num_img_patches)
    if num_patches_sqrt_float != int(num_patches_sqrt_float):
        print(f"Warning (Layer {layer_index}, Token '{token_string}'): Number of image patches ({num_img_patches}) is not a perfect square. Visualization might be inaccurate.")
        num_patches_sqrt = int(num_patches_sqrt_float); num_grid_patches = num_patches_sqrt * num_patches_sqrt; image_att_scores = image_att_scores[:num_grid_patches]
    else: num_patches_sqrt = int(num_patches_sqrt_float)
    if image_att_scores.shape[0] != num_patches_sqrt * num_patches_sqrt: print(f"Warning (Layer {layer_index}, Token '{token_string}'): Score shape mismatch after potential trim. {image_att_scores.shape[0]} vs {num_patches_sqrt*num_patches_sqrt}"); return None
    try: att_grid = image_att_scores.reshape(num_patches_sqrt, num_patches_sqrt)
    except Exception as e: print(f"Error reshaping scores: {e}"); return None
    att_grid_np = att_grid.cpu().float().numpy()
    upscaled_att_224 = F.interpolate(torch.tensor(att_grid_np).unsqueeze(0).unsqueeze(0), size=(img_height, img_width), mode=interpolation_mode, align_corners=False if interpolation_mode != 'nearest' else None).squeeze().numpy()
    fig, ax = plt.subplots(); ax.imshow(img_224); im = ax.imshow(upscaled_att_224, cmap='viridis', alpha=alpha); ax.axis('off'); fig.colorbar(im, ax=ax, label='Raw Attention Score')
    plt.title(f"Original: Layer {layer_index}, Token: '{token_string}'\n({interpolation_mode})")
    try: os.makedirs(os.path.dirname(output_path), exist_ok=True); plt.savefig(output_path, bbox_inches='tight', dpi=100)
    except Exception as e: print(f"Error saving original heatmap: {e}")
    finally: plt.close(fig)
    return upscaled_att_224

def normalize_map(att_map):
    """局部 Min-Max 归一化注意力图到 [0, 1]。"""
    min_val = np.min(att_map); max_val = np.max(att_map)
    if max_val > min_val: return (att_map - min_val) / (max_val - min_val)
    elif max_val == min_val and max_val > 0: return np.ones_like(att_map)
    else: return np.zeros_like(att_map)

def normalize_map_global(att_map, global_min, global_max):
    """使用全局最小/最大值进行归一化到 [0, 1]。"""
    if global_max > global_min:
        normalized = (np.clip(att_map, global_min, global_max) - global_min) / (global_max - global_min); return normalized
    elif global_max == global_min and global_max > 0: return np.ones_like(att_map) * (att_map >= global_max)
    else: return np.zeros_like(att_map)

# --- MODIFIED plot_attention_grid ---
def plot_attention_grid(
    maps_list_normalized,
    norm_type, global_min, global_max,
    base_image_224, figure_title, output_path,
    prompt: str, model_name: str, # Added prompt and model_name
    max_cols=GRID_MAX_COLS, alpha=HEATMAP_ALPHA
):
    """绘制包含多个注意力图的网格图像 (传入的图已归一化)。 Assumes maps_list_normalized is pre-sorted. Adds prompt/model info."""
    if not maps_list_normalized: return
    num_items = len(maps_list_normalized); num_cols = min(max_cols, num_items); num_rows = math.ceil(num_items / num_cols)
    base_fig_w = 4.0; base_fig_h = 5.0; fig_width = num_cols * base_fig_w; fig_height = num_rows * base_fig_h
    fig_height += 0.5 # Increase figure height slightly for info text
    fig, axes = plt.subplots(num_rows, num_cols, figsize=(fig_width, fig_height), squeeze=False)
    axes_flat = axes.flat; last_im = None;

    for i, (subplot_title, norm_map) in enumerate(maps_list_normalized):
        if i >= len(axes_flat): break
        ax = axes_flat[i]
        if base_image_224: ax.imshow(base_image_224)
        else: ax.set_facecolor('lightgray') # Placeholder if no image
        last_im = ax.imshow(norm_map, cmap='viridis', alpha=alpha, vmin=0, vmax=1)
        map_min_norm = np.min(norm_map); map_max_norm = np.max(norm_map); map_mean_norm = np.mean(norm_map); map_std_norm = np.std(norm_map); map_median_norm = np.median(norm_map)
        stats_text = f"Norm Min: {map_min_norm:.3f}, Max: {map_max_norm:.3f}\nMean: {map_mean_norm:.3f}, Std: {map_std_norm:.3f}, Median: {map_median_norm:.3f}"
        full_title = f"{subplot_title}\n{stats_text}"; ax.set_title(full_title, fontsize=9)
        ax.axis('off')

    for j in range(i + 1, num_rows * num_cols):
        if j < len(axes_flat): axes_flat[j].axis('off')

    if last_im:
        fig.subplots_adjust(right=0.88, hspace=0.45, wspace=0.1)
        cbar_ax = fig.add_axes([0.90, 0.15, 0.02, 0.7])
        cbar = fig.colorbar(last_im, cax=cbar_ax)
        if norm_type.lower() == 'globally': cbar_label = f'Attention Score\n({norm_type} Norm. [{global_min:.2f}, {global_max:.2f}])'
        else: cbar_label = f'Attention Score\n({norm_type} Normalized [0, 1])'
        cbar.set_label(cbar_label)

    # --- Add Figure Title and Info Text ---
    fig.suptitle(figure_title, fontsize=14, y=0.99)
    raw_info_text = f"Prompt: \"{prompt}\"  |  Model: {model_name}"
    info_text = textwrap.fill(raw_info_text, width=INFO_TEXT_WRAP_WIDTH)
    fig.text(0.5, 0.95, info_text, ha='center', va='top', fontsize=9, wrap=True)
    fig.subplots_adjust(top=0.90) # Adjust top margin
    # --- End Text Addition ---

    try: os.makedirs(os.path.dirname(output_path), exist_ok=True); plt.savefig(output_path, bbox_inches='tight', dpi=150)
    except Exception as e: print(f"Error saving grid plot '{figure_title}': {e}")
    finally: plt.close(fig)
# --- End MODIFIED plot_attention_grid ---

# --- MODIFIED plot_single_aggregated_attention ---
def plot_single_aggregated_attention(
    aggregated_map_raw, global_min, global_max,
    base_image_224, title, output_path,
    prompt: str, model_name: str, # Added prompt and model_name
    alpha=HEATMAP_ALPHA
):
    """绘制并保存单个聚合后的注意力图叠加 (使用全局归一化范围)。 Adds prompt/model info to title."""
    fig, ax = plt.subplots()
    norm_map = normalize_map_global(aggregated_map_raw, global_min, global_max)
    if base_image_224: ax.imshow(base_image_224)
    else: ax.set_facecolor('lightgray')
    im = ax.imshow(norm_map, cmap='viridis', alpha=alpha, vmin=0, vmax=1); ax.axis('off')
    cbar = fig.colorbar(im, ax=ax)
    cbar.set_label(f'Attention Score\n(Globally Norm. [{global_min:.2f}, {global_max:.2f}])')

    # --- Combine info into the main title ---
    map_min_norm = np.min(norm_map); map_max_norm = np.max(norm_map); map_mean_norm = np.mean(norm_map); map_std_norm = np.std(norm_map); map_median_norm = np.median(norm_map)
    stats_text = f"Norm Min: {map_min_norm:.3f}, Max: {map_max_norm:.3f}, Mean: {map_mean_norm:.3f}, Std: {map_std_norm:.3f}, Median: {map_median_norm:.3f}"
    wrapped_prompt = textwrap.fill(f"Prompt: \"{prompt}\"", width=INFO_TEXT_WRAP_WIDTH)
    wrapped_model = textwrap.fill(f"Model: {model_name}", width=INFO_TEXT_WRAP_WIDTH)
    full_title = f"{title}\n{wrapped_prompt}\n{wrapped_model}\n{stats_text}"
    plt.title(full_title, fontsize=9)
    # --- End Title Modification ---

    plt.tight_layout(rect=[0, 0.03, 1, 0.95]) # Adjust layout

    try: os.makedirs(os.path.dirname(output_path), exist_ok=True); plt.savefig(output_path, bbox_inches='tight', dpi=150); print(f"  Saved single plot: {os.path.basename(output_path)}")
    except Exception as e: print(f"Error saving single aggregated heatmap '{title}': {e}")
    finally: plt.close(fig)
# --- End MODIFIED plot_single_aggregated_attention ---

def main():
    # Updated description
    parser = argparse.ArgumentParser(description="Visualize PaliGemma-2 Prompt Attention (v5 - Local Path, Raw Orig, Dual Norm Avg, Sorted Layers, with Info, New Dir Name)")
    parser.add_argument("--model_name", type=str, default="google/paligemma2-3b-pt-224", help="Hugging Face model identifier")
    parser.add_argument("--prompt", type=str, default="<image>coke", help="Text prompt (should include <image> placeholder)")
    parser.add_argument("--image_path", type=str, default="coke.png", help="Path to image")
    parser.add_argument("--output_dir_base", type=str, default=".", help="Base directory for output folders")
    parser.add_argument("--device", type=str, default="cuda" if torch.cuda.is_available() else "cpu", help="Device")
    parser.add_argument("--interpolation", type=str, default=DEFAULT_INTERPOLATION, choices=['nearest', 'bilinear'], help="Interpolation mode")
    args = parser.parse_args()

    device = torch.device(args.device)
    dtype = torch.bfloat16
    if dtype == torch.bfloat16 and not torch.cuda.is_bf16_supported(): print("Warning: BF16 is not supported on this GPU. Falling back to FP32."); dtype = torch.float32
    elif dtype == torch.bfloat16: print("BF16 is supported and will be used.")
    else: dtype = torch.float32
    print(f"Using device: {device}, dtype: {dtype}")

    # --- MODIFIED: Output directory naming ---
    # Clean the prompt for use in the directory name
    safe_prompt_name = make_safe_dirname(args.prompt)
    # Extract the base model name (e.g., "paligemma2-3b-pt-224" from "google/paligemma2-3b-pt-224")
    model_base_name = get_model_basename(args.model_name)
    # Combine model base name and safe prompt name
    main_output_dir_name = f"{model_base_name}_{safe_prompt_name}"
    main_output_dir = os.path.join(args.output_dir_base, main_output_dir_name)
    # --- End MODIFICATION ---

    original_views_dir = os.path.join(main_output_dir, "original_views")
    average_views_dir = os.path.join(main_output_dir, "average_views")
    try:
        os.makedirs(main_output_dir, exist_ok=True); os.makedirs(original_views_dir, exist_ok=True); os.makedirs(average_views_dir, exist_ok=True)
        print(f"Main output directory: '{main_output_dir}'"); print(f"Original views directory: '{original_views_dir}'"); print(f"Average views directory: '{average_views_dir}'")
    except OSError as e: print(f"Error creating directories: {e}"); return

    print(f"Loading `{args.model_name}` on {device} with dtype={dtype}...")
    try:
        model = PaliGemmaForConditionalGeneration.from_pretrained(args.model_name, torch_dtype=dtype).to(device)
        processor = AutoProcessor.from_pretrained(args.model_name)
        print(f"Model class: {model.__class__.__name__}")
    except Exception as e: print(f"Error loading model/processor: {e}"); return

    inputs_on_device, _original_img_size, prompt_token_list = inspect_inputs(processor, args.image_path, args.prompt, device, dtype)
    if inputs_on_device is None: print("Failed input inspection."); return
    if not prompt_token_list: print("No prompt tokens identified. Cannot visualize prompt attention."); return
    base_image_224 = None
    if 'pixel_values' in inputs_on_device:
         try: pv = inputs_on_device['pixel_values'][0]; base_image_224 = T.ToPILImage()(pv.cpu().float().clamp(0, 1))
         except Exception as e: print(f"Error creating base 224x224 image: {e}")
    if base_image_224 is None and g_num_image_patches > 0: print("Warning: Could not create base image, image attention plots might be affected.");

    attentions_all_layers = get_all_attention_maps(model, inputs_on_device, device, dtype)

    all_attention_grids_raw = {}
    if attentions_all_layers is not None:
        num_layers = len(attentions_all_layers); num_prompt_tokens = len(prompt_token_list)
        print(f"\nStarting aligned visualization ({args.interpolation}) for {num_layers} layers, {num_prompt_tokens} tokens...")
        total_images = num_layers * num_prompt_tokens; img_count = 0
        for layer_idx, layer_att_map in enumerate(attentions_all_layers):
            print(f"--- Processing Layer {layer_idx} ---")
            for token_idx, token_str in enumerate(prompt_token_list):
                img_count += 1; print(f"  Processing Token {token_idx+1}/{num_prompt_tokens}: '{token_str}' (Image {img_count}/{total_images})")
                safe_token_str_filename = make_safe_filename(token_str) # Use less aggressive cleaning for filenames
                output_filename = f"layer_{layer_idx}_token_{token_idx}_{safe_token_str_filename}_{args.interpolation}.png"
                original_output_path = os.path.join(original_views_dir, output_filename)
                grid_data_raw = calculate_and_visualize_single_token_attention(
                    layer_att_map, inputs_on_device, original_output_path, layer_index=layer_idx,
                    token_index_in_prompt=token_idx, token_string=token_str, interpolation_mode=args.interpolation,
                    alpha=HEATMAP_ALPHA, average_heads=AVERAGE_HEADS
                )
                if grid_data_raw is not None: all_attention_grids_raw[(layer_idx, token_idx)] = grid_data_raw
        print(f"\nFinished generating {img_count} original visualizations.")

        if all_attention_grids_raw and (base_image_224 or g_num_image_patches == 0):
            print("\n--- Generating Aggregated Grid Visualizations (Global + Local Norm, Saving to 'average_views') ---")

            # --- a) 按 Token 聚合 (MODIFIED for sorting & passing info) ---
            print("  Aggregating attention per token (averaging layers)...")
            aggregated_per_token_raw = defaultdict(list)
            for (layer_idx, token_idx), grid_raw in all_attention_grids_raw.items(): aggregated_per_token_raw[token_idx].append(grid_raw)
            token_avg_maps_raw = {}; valid_token_maps = []
            sorted_token_indices = sorted(aggregated_per_token_raw.keys())
            for token_idx in sorted_token_indices:
                 grids_raw = aggregated_per_token_raw[token_idx]
                 if grids_raw: mean_map_raw = np.mean(np.stack(grids_raw), axis=0); token_avg_maps_raw[token_idx] = mean_map_raw; valid_token_maps.append(mean_map_raw)
            token_global_min = np.min(valid_token_maps) if valid_token_maps else 0; token_global_max = np.max(valid_token_maps) if valid_token_maps else 1
            print(f"    Token Avg Global Range: [{token_global_min:.4f}, {token_global_max:.4f}]")
            token_avg_maps_global_norm_list = []; token_avg_maps_local_norm_list = []
            for token_idx in sorted_token_indices:
                if token_idx in token_avg_maps_raw:
                    raw_map = token_avg_maps_raw[token_idx]; global_norm_map = normalize_map_global(raw_map, token_global_min, token_global_max); local_norm_map = normalize_map(raw_map)
                    # Check bounds for prompt_token_list
                    if 0 <= token_idx < len(prompt_token_list):
                        title = f"Token {token_idx}: '{prompt_token_list[token_idx]}'\n(Avg Layers)"
                    else:
                         title = f"Token {token_idx}: [Index Error]\n(Avg Layers)"
                    token_avg_maps_global_norm_list.append((title, global_norm_map)); token_avg_maps_local_norm_list.append((title, local_norm_map))
            if token_avg_maps_global_norm_list:
                grid_title = f"Average Attention per Prompt Token (Layers Averaged, Globally Normalized, {args.interpolation})"
                grid_filename = f"grid_avg_per_token_globalnorm_{args.interpolation}.png"; grid_output_path = os.path.join(average_views_dir, grid_filename)
                plot_attention_grid(token_avg_maps_global_norm_list, "Globally", token_global_min, token_global_max, base_image_224, grid_title, grid_output_path, prompt=args.prompt, model_name=args.model_name)
            if token_avg_maps_local_norm_list:
                grid_title = f"Average Attention per Prompt Token (Layers Averaged, Locally Normalized, {args.interpolation})"
                grid_filename = f"grid_avg_per_token_localnorm_{args.interpolation}.png"; grid_output_path = os.path.join(average_views_dir, grid_filename)
                plot_attention_grid(token_avg_maps_local_norm_list, "Locally", 0, 1, base_image_224, grid_title, grid_output_path, prompt=args.prompt, model_name=args.model_name)

            # --- b) 按 Layer 聚合 (MODIFIED for sorting & passing info) ---
            print("  Aggregating attention per layer (averaging tokens)...")
            aggregated_per_layer_raw = defaultdict(list)
            for (layer_idx, token_idx), grid_raw in all_attention_grids_raw.items(): aggregated_per_layer_raw[layer_idx].append(grid_raw)
            layer_avg_maps_raw = {}; valid_layer_maps = []
            sorted_layers = sorted(aggregated_per_layer_raw.keys())
            for layer_idx in sorted_layers:
                 grids_raw = aggregated_per_layer_raw[layer_idx]
                 if grids_raw: mean_map_raw = np.mean(np.stack(grids_raw), axis=0); layer_avg_maps_raw[layer_idx] = mean_map_raw; valid_layer_maps.append(mean_map_raw)
            layer_global_min = np.min(valid_layer_maps) if valid_layer_maps else 0; layer_global_max = np.max(valid_layer_maps) if valid_layer_maps else 1
            print(f"    Layer Avg Global Range: [{layer_global_min:.4f}, {layer_global_max:.4f}]")
            layer_avg_maps_global_norm_list = []; layer_avg_maps_local_norm_list = []
            for layer_idx in sorted_layers:
                if layer_idx in layer_avg_maps_raw:
                    raw_map = layer_avg_maps_raw[layer_idx]; global_norm_map = normalize_map_global(raw_map, layer_global_min, layer_global_max); local_norm_map = normalize_map(raw_map)
                    title = f"Layer {layer_idx}\n(Avg Tokens)"; layer_avg_maps_global_norm_list.append((title, global_norm_map)); layer_avg_maps_local_norm_list.append((title, local_norm_map))
            if layer_avg_maps_global_norm_list:
                grid_title = f"Average Attention per Layer (Tokens Averaged, Globally Normalized, {args.interpolation})"
                grid_filename = f"grid_avg_per_layer_globalnorm_{args.interpolation}.png"; grid_output_path = os.path.join(average_views_dir, grid_filename)
                plot_attention_grid(layer_avg_maps_global_norm_list, "Globally", layer_global_min, layer_global_max, base_image_224, grid_title, grid_output_path, max_cols=6, prompt=args.prompt, model_name=args.model_name)
            if layer_avg_maps_local_norm_list:
                grid_title = f"Average Attention per Layer (Tokens Averaged, Locally Normalized, {args.interpolation})"
                grid_filename = f"grid_avg_per_layer_localnorm_{args.interpolation}.png"; grid_output_path = os.path.join(average_views_dir, grid_filename)
                plot_attention_grid(layer_avg_maps_local_norm_list, "Locally", 0, 1, base_image_224, grid_title, grid_output_path, max_cols=6, prompt=args.prompt, model_name=args.model_name)

            # --- c) 全局聚合 (MODIFIED passing info) ---
            print("  Calculating global average attention...")
            all_grids_list_raw = list(all_attention_grids_raw.values())
            if all_grids_list_raw:
                global_mean_map_raw = np.mean(np.stack(all_grids_list_raw), axis=0)
                global_min_agg = np.min(global_mean_map_raw); global_max_agg = np.max(global_mean_map_raw)
                print(f"    Global Avg Raw Range: [{global_min_agg:.4f}, {global_max_agg:.4f}]")
                agg_title = f"Global Average Attention (Avg Layers & Tokens, Globally Normalized, {args.interpolation})" # Shortened title
                agg_filename = f"aggregated_global_average_norm_{args.interpolation}.png"; agg_output_path = os.path.join(average_views_dir, agg_filename)
                plot_single_aggregated_attention(global_mean_map_raw, global_min_agg, global_max_agg, base_image_224, agg_title, agg_output_path, prompt=args.prompt, model_name=args.model_name)

            print("--- Finished Aggregated Visualizations ---")
        elif not all_attention_grids_raw: print("\nSkipping aggregated visualizations: no attention grids collected.")
        elif not base_image_224 and g_num_image_patches > 0: print("\nSkipping aggregated visualizations: base image missing and image patches exist.")
        else: print("\nSkipping aggregated visualizations for unknown reason.")

    else: print("Failed to retrieve attention maps.")

if __name__ == "__main__":
    main()